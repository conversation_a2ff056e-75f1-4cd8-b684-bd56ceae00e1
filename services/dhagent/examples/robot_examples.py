"""
Robot service usage examples demonstrating Clean Architecture implementation.

This module shows how to use the robot service for various operations,
following the established patterns and best practices.
"""

import logging

from src.external.robot.robot import IdType, SearchType
from src.usecases.robot import default_robot_service
from src.usecases.robot.service import RobotConversationServiceError

logger = logging.getLogger(__name__)


async def example_get_user_authorization():
    """Example: Get user authorization tokens."""
    robot_service = default_robot_service

    try:
        # Get authorization for multiple services
        response = await robot_service.get_user_authorization(
            user_id="jingxuyang",
            service_ids_with_scopes={
                "1TmoBZo2H9TXGvdOkHDc": ["create_ticket:read"],
                "1TmoBZo2H9TXGvdOkH12": ["ticket:write"],
            },
            search_type=SearchType.LDAP_NAME,
        )

        print(f"Authorization response code: {response.code}")
        print(f"Authorization message: {response.msg}")

        for service_token in response.data["services"]:
            if service_token.error:
                print(f"Service {service_token.id}: Error - {service_token.error}")
            else:
                print(f"Service {service_token.id}: Token expires in {service_token.expires_in}s")

    except RobotConversationServiceError as e:
        logger.error(f"Service error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")


async def example_create_conversation() -> int:
    """Example: Create a new conversation."""
    robot_service = default_robot_service

    try:
        response = await robot_service.create_conversation(
            title="DeepHealAgent 服务变更 HITL 测试", ids=["zhouzheng5"], scenario="deepheal", id_type=IdType.LDAP_NAME
        )

        conversation = response.data
        print(f"Created conversation {conversation.id}: '{conversation.title}'")
        print(f"Chat ID: {conversation.chat_id}")
        print(f"Assistant: {conversation.primary_assistant_display_name}")
        print(f"Metadata: {conversation.metadata}")

        return conversation.id

    except RobotConversationServiceError as e:
        logger.error(f"Service error: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return None


async def example_invite_users(conversation_id: int):
    """Example: Invite users to conversation."""
    robot_service = default_robot_service

    try:
        response = await robot_service.invite_users_to_conversation(
            conversation_id=conversation_id, ids=["zhenghe"], id_type=IdType.LDAP_NAME
        )

        print(f"Invitation response code: {response.code}")
        invalid_ids = response.data.get("invalid_id_list", [])
        if invalid_ids:
            print(f"Invalid member IDs: {invalid_ids}")
        else:
            print("All members invited successfully")

    except RobotConversationServiceError as e:
        logger.error(f"Service error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")


async def example_update_conversation_title(conversation_id: int):
    """Example: Update conversation title."""
    robot_service = default_robot_service

    try:
        response = await robot_service.update_conversation_title(
            conversation_id=conversation_id, title="测试更新标题 - DeepHeal Diagnosis"
        )

        conversation = response.data
        print(f"Updated conversation {conversation.id} title to: '{conversation.title}'")

    except RobotConversationServiceError as e:
        logger.error(f"Service error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")


async def example_send_message(conversation_id: int):
    """Example: Send a message to conversation."""
    robot_service = default_robot_service

    try:
        # Example message with Feishu card template
        messages = [
            {
                "sender": {"role": "agent", "name": "dhagent"},
                "recipient": {"role": "notifier", "name": ""},
                "recipient_scope": "single_group",
                "contents": [
                    {
                        "mime_type": "text/plain",
                        "body": {
                            "text": "罗伯特刚刚检测到您发布了「xxx」服务，现在我们将自动巡检该服务的日志、"
                            "调用链、监控指标、告警等信息，协助您确认发布后是否存存在异常。"
                        },
                    }
                ],
                "metadata": {},
            }
        ]

        response = await robot_service.send_conversation_message(conversation_id=conversation_id, messages=messages)

        print(f"Message sent response code: {response.code}")
        print(f"Message sent response: {response.msg}")

    except RobotConversationServiceError as e:
        logger.error(f"Service error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")


async def example_get_conversation_history(conversation_id: int):
    """Example: Get conversation history."""
    robot_service = default_robot_service

    try:
        response = await robot_service.get_conversation_history(conversation_id)

        history = response.data
        print(f"Conversation history: {history}")

    except RobotConversationServiceError as e:
        logger.error(f"Service error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")


async def example_complete_workflow():
    """Example: Complete workflow demonstrating all operations."""
    print("=== Robot Service Complete Workflow Example ===\n")

    # 1. Get user authorization
    print("1. Getting user authorization...")
    await example_get_user_authorization()
    print()

    # 2. Create conversation
    print("2. Creating conversation...")
    conversation_id = await example_create_conversation()
    if not conversation_id:
        print("Failed to create conversation, stopping workflow")
        return
    print()

    # 3. Invite users
    print("3. Inviting users to conversation...")
    await example_invite_users(conversation_id)
    print()

    # 4. Update conversation title
    print("4. Updating conversation title...")
    await example_update_conversation_title(conversation_id)
    print()

    # 5. Send message
    print("5. Sending message...")
    await example_send_message(conversation_id)
    print()

    # 6. Get conversation history
    print("6. Getting conversation history...")
    await example_get_conversation_history(conversation_id)
    print()

    print("=== Workflow completed ===")


async def example_error_handling():
    """Example: Error handling patterns."""
    robot_service = default_robot_service

    try:
        # This should fail with validation error
        await robot_service.get_user_authorization(
            user_id="",  # Empty user ID
            service_ids_with_scopes={},  # Empty services
        )
    except RobotConversationServiceError as e:
        print(f"Caught service error (expected): {e}")

    try:
        # This should fail with not found error
        await robot_service.get_conversation_history(999999)
    except RobotConversationServiceError as e:
        print(f"Caught service error for non-existent conversation: {e}")


# if __name__ == "__main__":

# 创建会话
# conversation_id = asyncio.run(example_create_conversation())
# print(f"Conversation ID: {conversation_id}")
#
# # 会话拉人
# # asyncio.run(example_invite_users(conversation_id))
#
# # 发送消息
# asyncio.run(example_send_message(conversation_id))
#
# # 修改title
# asyncio.run(example_update_conversation_title(conversation_id))
#
# # 查询会话历史
# asyncio.run(example_get_conversation_history(conversation_id))

# 获取用户授权token
# asyncio.run(example_get_user_authorization())
