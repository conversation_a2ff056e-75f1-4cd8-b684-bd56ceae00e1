"""Conversation service interface - Domain layer contracts."""

from abc import ABC, abstractmethod

from src.external.robot.robot import (
    AddMembersResponse,
    ConversationHistoryResponse,
    ConversationResponse,
    IdType,
    SearchType,
    SendMessageResponse,
    UserCredentialsResponse,
)


class IConversationService(ABC):
    """
    Conversation service interface defining business operations contract.
    
    This interface defines the contract for Conversation business operations,
    following Clean Architecture principles by keeping the domain layer
    independent of external frameworks and infrastructure.
    """

    @abstractmethod
    async def get_user_authorization(
        self,
        user_id: str,
        service_ids_with_scopes: dict[str, list[str]],
        search_type: SearchType = SearchType.LDAP_NAME,
        said_token: str | None = None,
    ) -> UserCredentialsResponse:
        """
        Get user authorization tokens for specified services.

        Args:
            user_id: User identifier
            service_ids_with_scopes: Mapping of service IDs to required scopes
            search_type: Type of user identifier
            said_token: Optional SAID token for Client-Assertion

        Returns:
            User credentials response with service tokens

        Raises:
            ConversationServiceError: If the operation fails
        """
        pass

    @abstractmethod
    async def create_conversation(
        self, 
        title: str, 
        ids: list[str], 
        scenario: str = "deepheal", 
        id_type: IdType = IdType.LDAP_NAME
    ) -> ConversationResponse:
        """
        Create a new conversation.

        Args:
            title: Conversation title
            ids: List of member IDs to include
            scenario: Conversation scenario (default: "deepheal")
            id_type: Type of member identifiers

        Returns:
            Created conversation response

        Raises:
            ConversationServiceError: If the operation fails
        """
        pass

    @abstractmethod
    async def invite_users_to_conversation(
        self, 
        conversation_id: int, 
        ids: list[str], 
        id_type: IdType = IdType.LDAP_NAME
    ) -> AddMembersResponse:
        """
        Invite users to join an existing conversation.

        Args:
            conversation_id: Target conversation ID
            ids: List of member IDs to invite
            id_type: Type of member identifiers

        Returns:
            Members addition response

        Raises:
            ConversationServiceError: If the operation fails
        """
        pass

    @abstractmethod
    async def update_conversation_title(self, conversation_id: int, title: str) -> ConversationResponse:
        """
        Update conversation title.

        Args:
            conversation_id: Target conversation ID
            title: New conversation title

        Returns:
            Updated conversation response

        Raises:
            ConversationServiceError: If the operation fails
        """
        pass

    @abstractmethod
    async def send_conversation_message(
        self, 
        conversation_id: int, 
        messages: list[dict], 
        parent_id: int | None = None
    ) -> SendMessageResponse:
        """
        Send a message to a conversation.

        Args:
            conversation_id: Target conversation ID
            messages: List of message objects to send
            parent_id: Optional parent message ID

        Returns:
            Message sending response

        Raises:
            ConversationServiceError: If the operation fails
        """
        pass

    @abstractmethod
    async def get_conversation_history(self, conversation_id: int) -> ConversationHistoryResponse:
        """
        Get conversation history.

        Args:
            conversation_id: Target conversation ID

        Returns:
            Conversation history response

        Raises:
            ConversationServiceError: If the operation fails
        """
        pass
