"""Robot use cases package."""

from src.usecases.shared.conversation_service_interface import IConversationService

from .conversation_service import RobotConversationService, RobotConversationServiceError
from src.external.idaas.client import ID<PERSON><PERSON><PERSON>
from src.external.robot.http_client import RobotHttpClient
from ...config import settings

_default_robot_conversation_service: IConversationService | None = None


def create_default_robot_conversation_service() -> IConversationService:
    """
    Create default robot service instance in the use case layer.

    This function assembles the robot service with its dependencies,
    following Clean Architecture principles by keeping the business
    logic assembly in the use case layer.

    Returns:
        Default robot service instance
    """
    # Create infrastructure dependencies
    idaas_client = IDaaSClient()
    idaas_client.initialize(
        client_id=settings.idaas_dhagent_client_id,
        client_secret=settings.idaas_dhagent_client_secret,
        service_id=settings.idaas_robot_service_id,
        endpoint=settings.idaas_host,
        scopes=settings.idaas_robot_service_scopes,
    )

    http_client = RobotHttpClient(
        idaas_client=idaas_client,
        base_url=settings.robot_api_base_url,
        timeout=settings.robot_api_timeout
    )

    # Create and return business logic service
    return RobotConversationService(http_client)


def get_default_robot_conversation_service() -> IConversationService:
    """
    Get or create the default robot service instance (lazy initialization).

    Returns:
        Default robot service instance
    """
    global _default_robot_conversation_service
    if _default_robot_conversation_service is None:
        _default_robot_conversation_service = create_default_robot_conversation_service()
    return _default_robot_conversation_service


default_robot_service = get_default_robot_conversation_service()

__all__ = [
    "IConversationService",
    "RobotConversationService",
    "RobotConversationServiceError",
    "default_robot_service",
]
