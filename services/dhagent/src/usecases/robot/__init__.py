"""Robot use cases package."""

from src.external.idaas.client import <PERSON><PERSON><PERSON><PERSON>
from src.external.robot.config import (
    ROBOT_API_BASE_URL,
    ROBOT_API_TIMEOUT,
    ROBOT_IDAAS_CLIENT_ID,
    ROBOT_IDAAS_CLIENT_SECRET,
    ROBOT_IDAAS_HOST,
    ROBOT_IDAAS_SCOPES,
    ROBOT_IDAAS_SERVICE_ID,
)
from src.external.robot.http_client import RobotHttpClient
from src.usecases.shared.robot_service_interface import IRobotService

from .robot_service import RobotService, RobotServiceError


def create_default_robot_service() -> IRobotService:
    """
    Create default robot service instance in the use case layer.

    This function assembles the robot service with its dependencies,
    following Clean Architecture principles by keeping the business
    logic assembly in the use case layer.

    Returns:
        Default robot service instance
    """
    # Create infrastructure dependencies
    idaas_client = IDaaSClient()
    idaas_client.initialize(
        client_id=ROBOT_IDAAS_CLIENT_ID,
        client_secret=ROBOT_IDAAS_CLIENT_SECRET,
        service_id=ROBOT_IDAAS_SERVICE_ID,
        endpoint=ROBOT_IDAAS_HOST,
        scopes=ROBOT_IDAAS_SCOPES,
    )

    http_client = RobotHttpClient(
        base_url=ROBOT_API_BASE_URL,
        idaas_client=idaas_client,
        timeout=ROBOT_API_TIMEOUT
    )

    # Create and return business logic service
    return RobotService(http_client)


# Create default robot service instance
default_robot_service = create_default_robot_service()

__all__ = [
    "IRobotService",
    "RobotService",
    "RobotServiceError",
    "default_robot_service",
    "create_default_robot_service",
]
