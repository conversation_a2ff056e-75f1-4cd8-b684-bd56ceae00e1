"""Robot use cases package."""

from src.usecases.shared.robot_service_interface import IRobotService

from .robot_service import RobotService, RobotServiceError
from src.external.idaas.client import IDaaSClient
from src.external.robot.http_client import RobotHttpClient
from ...config import settings

_default_robot_service: IRobotService | None = None

def create_default_robot_service() -> IRobotService:
    """
    Create default robot service instance in the use case layer.

    This function assembles the robot service with its dependencies,
    following Clean Architecture principles by keeping the business
    logic assembly in the use case layer.

    Returns:
        Default robot service instance
    """
    # Create infrastructure dependencies
    idaas_client = IDaaSClient().initialize()
    idaas_client.initialize(
        client_id=settings.idaas_dhagent_client_id,
        client_secret=settings.idaas_dhagent_client_secret,
        service_id=settings.idaas_robot_service_id,
        endpoint=settings.idaas_host,
        scopes=settings.idaas_robot_service_scopes,
    )

    http_client = RobotHttpClient(
        idaas_client=idaas_client,
        base_url=settings.robot_api_base_url,
        timeout=settings.robot_api_timeout
    )

    # Create and return business logic service
    return RobotService(http_client)


def get_default_robot_service() -> IRobotService:
    """
    Get or create the default robot service instance (lazy initialization).

    Returns:
        Default robot service instance
    """
    global _default_robot_service
    if _default_robot_service is None:
        _default_robot_service = create_default_robot_service()
    return _default_robot_service


default_robot_service = get_default_robot_service()

__all__ = [
    "IRobotService",
    "RobotService",
    "RobotServiceError",
    "default_robot_service",
]
