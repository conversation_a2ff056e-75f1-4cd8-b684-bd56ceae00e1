"""Robot service - Use cases for robot operations."""

import logging

from src.entities.robot import (
    AddMembersRequest,
    AddMembersResponse,
    ConversationHistoryResponse,
    ConversationResponse,
    CreateConversationRequest,
    IdType,
    SearchType,
    SendMessageRequest,
    SendMessageResponse,
    ServiceCredential,
    UpdateConversationRequest,
    UserCredentialsRequest,
    UserCredentialsResponse,
)
from src.usecases.shared.robot_service_interface import IRobotService

logger = logging.getLogger(__name__)


class RobotService(IRobotService):
    """
    Robot service containing business logic for robot operations.

    This service orchestrates robot operations and implements business rules,
    following Clean Architecture principles.
    """

    def __init__(self, http_client):
        """
        Initialize robot service.

        Args:
            http_client: Robot HTTP client for external API communication
        """
        self._http_client = http_client

    async def get_user_authorization(
        self,
        user_id: str,
        service_ids_with_scopes: dict[str, list[str]],
        search_type: SearchType = SearchType.LDAP_NAME,
        said_token: str | None = None,
    ) -> UserCredentialsResponse:
        """
        Get user authorization tokens for specified services.

        Args:
            user_id: User identifier
            service_ids_with_scopes: Mapping of service IDs to required scopes
            search_type: Type of user identifier
            said_token: Optional SAID token for Client-Assertion

        Returns:
            User credentials response with service tokens

        Raises:
            RobotServiceError: If the operation fails
        """
        try:
            # Convert service mapping to credential objects
            services = [
                ServiceCredential(id=service_id, scopes=scopes)
                for service_id, scopes in service_ids_with_scopes.items()
            ]

            request = UserCredentialsRequest(search_type=search_type, user_id=user_id, services=services)

            logger.info(f"Getting user authorization for user {user_id} with {len(services)} services")

            response = await self._http_client.get_user_credentials(request, said_token)

            logger.info(f"Successfully retrieved authorization for user {user_id}")
            return response

        except Exception as e:
            logger.error(f"Failed to get user authorization for {user_id}: {e}")
            raise RobotServiceError(f"Failed to get user authorization: {e}") from e

    async def create_conversation(
        self, title: str, ids: list[str], scenario: str = "deepheal", id_type: IdType = IdType.LDAP_NAME
    ) -> ConversationResponse:
        """
        Create a new robot conversation.

        Args:
            title: Conversation title
            ids: List of member IDs to include
            scenario: Conversation scenario (default: "deepheal")
            id_type: Type of member identifiers

        Returns:
            Created conversation response

        Raises:
            RobotServiceError: If the operation fails
        """
        try:
            # Build metadata with member information
            metadata = {"members": {"id_type": id_type.value, "ids": ids}}

            request = CreateConversationRequest(title=title, scenario=scenario, metadata=metadata)

            logger.info(f"Creating conversation '{title}' with {len(ids)} members")

            response = await self._http_client.create_conversation(request)

            logger.info(f"Successfully created conversation {response.data.id}: '{title}'")
            return response

        except Exception as e:
            logger.error(f"Failed to create conversation '{title}': {e}")
            raise RobotServiceError(f"Failed to create conversation: {e}") from e

    async def invite_users_to_conversation(
        self, conversation_id: int, ids: list[str], id_type: IdType = IdType.LDAP_NAME
    ) -> AddMembersResponse:
        """
        Invite users to join an existing conversation.

        Args:
            conversation_id: Target conversation ID
            ids: List of member IDs to invite
            id_type: Type of member identifiers

        Returns:
            Members addition response

        Raises:
            RobotServiceError: If the operation fails
        """
        try:
            request = AddMembersRequest(id_type=id_type, ids=ids)

            logger.info(f"Inviting {len(ids)} users to conversation {conversation_id}")

            response = await self._http_client.add_conversation_members(conversation_id, request)

            invalid_count = len(response.data.get("invalid_id_list", []))
            if invalid_count > 0:
                logger.warning(f"Conversation {conversation_id}: {invalid_count} invalid member IDs")

            logger.info(f"Successfully processed invitation for conversation {conversation_id}")
            return response

        except Exception as e:
            logger.error(f"Failed to invite users to conversation {conversation_id}: {e}")
            raise RobotServiceError(f"Failed to invite users: {e}") from e

    async def update_conversation_title(self, conversation_id: int, title: str) -> ConversationResponse:
        """
        Update conversation title.

        Args:
            conversation_id: Target conversation ID
            title: New conversation title

        Returns:
            Updated conversation response

        Raises:
            RobotServiceError: If the operation fails
        """
        try:
            request = UpdateConversationRequest(title=title)

            logger.info(f"Updating conversation {conversation_id} title to '{title}'")

            response = await self._http_client.update_conversation(conversation_id, request)

            logger.info(f"Successfully updated conversation {conversation_id} title")
            return response

        except Exception as e:
            logger.error(f"Failed to update conversation {conversation_id} title: {e}")
            raise RobotServiceError(f"Failed to update conversation: {e}") from e

    async def send_conversation_message(
        self, conversation_id: int, messages: list[dict], parent_id: int | None = None
    ) -> SendMessageResponse:
        """
        Send a message to a conversation.

        Args:
            conversation_id: Target conversation ID
            messages: List of message objects to send
            parent_id: Optional parent message ID

        Returns:
            Message sending response

        Raises:
            RobotServiceError: If the operation fails
        """
        try:
            request = SendMessageRequest(parent_id=parent_id, messages=messages)

            logger.info(f"Sending {len(messages)} message(s) to conversation {conversation_id}")

            response = await self._http_client.send_message(conversation_id, request)

            logger.info(f"Successfully sent message(s) to conversation {conversation_id}")
            return response

        except Exception as e:
            logger.error(f"Failed to send message to conversation {conversation_id}: {e}")
            raise RobotServiceError(f"Failed to send message: {e}") from e

    async def get_conversation_history(self, conversation_id: int) -> ConversationHistoryResponse:
        """
        Get conversation history.

        Args:
            conversation_id: Target conversation ID

        Returns:
            Conversation history response

        Raises:
            RobotServiceError: If the operation fails
        """
        try:
            logger.info(f"Getting history for conversation {conversation_id}")

            response = await self._http_client.get_conversation_history(conversation_id)

            return response

        except Exception as e:
            logger.error(f"Failed to get conversation {conversation_id} history: {e}")
            raise RobotServiceError(f"Failed to get conversation history: {e}") from e


class RobotServiceError(Exception):
    """Exception raised by robot service operations."""

    def __init__(self, message: str, cause: Exception | None = None):
        super().__init__(message)
        self.message = message
        self.cause = cause


