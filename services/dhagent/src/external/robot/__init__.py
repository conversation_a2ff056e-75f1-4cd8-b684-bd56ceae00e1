"""
Robot client package - Clean Architecture implementation for robot operations.

This package provides a clean, well-structured interface for robot operations
following Clean Architecture principles with proper separation of concerns.

Usage:
    from src.external.robot import default_robot_service

    # Get user authorization
    response = await default_robot_service.get_user_authorization(
        user_id="jingxuyang",
        service_ids_with_scopes={
            "service1": ["read", "write"],
            "service2": ["admin"]
        }
    )

    # Create conversation
    conversation = await default_robot_service.create_conversation(
        title="Support Session",
        ids=["user1", "user2"]
    )
"""

from src.external.idaas.client import IDaaSClient
from src.usecases.robot.robot_service import RobotService

from .config import (
    ROBOT_API_BASE_URL,
    ROBOT_API_TIMEOUT,
    ROBOT_IDAAS_CLIENT_ID,
    ROBOT_IDAAS_CLIENT_SECRET,
    ROBOT_IDAAS_HOST,
    ROBOT_IDAAS_SCOPES,
    ROBOT_IDAAS_SERVICE_ID,
)
from .http_client import RobotHttpClient

# Create dependencies
_idaas_client = IDaaSClient()
_idaas_client.initialize(
    client_id=ROBOT_IDAAS_CLIENT_ID,
    client_secret=ROBOT_IDAAS_CLIENT_SECRET,
    service_id=ROBOT_IDAAS_SERVICE_ID,
    endpoint=ROBOT_IDAAS_HOST,
    scopes=ROBOT_IDAAS_SCOPES,
)

_http_client = RobotHttpClient(
    base_url=ROBOT_API_BASE_URL,
    idaas_client=_idaas_client,
    timeout=ROBOT_API_TIMEOUT
)

# Create default robot service instance
default_robot_service = RobotService(_http_client)

# Export main interfaces
__all__ = [
    "default_robot_service",
]
