"""Robot HTTP client - Infrastructure layer implementation."""

import logging
from typing import Any

import httpx
from pydantic import ValidationError

from src.entities.robot import (
    AddMembersRequest,
    AddMembersResponse,
    ConversationHistoryResponse,
    ConversationResponse,
    CreateConversationRequest,
    SendMessageRequest,
    SendMessageResponse,
    UpdateConversationRequest,
    UserCredentialsRequest,
    UserCredentialsResponse,
)
from src.external.idaas.client import IDaaSClient


logger = logging.getLogger(__name__)


class RobotClientError(Exception):
    """Base exception for robot client operations."""

    def __init__(self, message: str, status_code: int | None = None, details: dict | None = None):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.details = details or {}


class RobotAuthenticationError(RobotClientError):
    """Raised when authentication fails."""
    pass


class RobotNetworkError(RobotClientError):
    """Raised when network operations fail."""
    pass


class RobotNotFoundError(RobotClientError):
    """Raised when requested resource is not found."""
    pass


class RobotValidationError(RobotClientError):
    """Raised when request validation fails."""
    pass


class RobotHttpClient:
    """
    HTTP client for robot API operations.

    This class handles HTTP communication with the robot service,
    including authentication, request/response handling, and error management.
    """

    def __init__(self, base_url: str, idaas_client: IDaaSClient, timeout: float = 30.0):
        """
        Initialize robot HTTP client.

        Args:
            base_url: Base URL for robot API
            idaas_client: IDaaS client for authentication
            timeout: Request timeout in seconds
        """
        self.base_url = base_url.rstrip("/")
        self.timeout = timeout
        self.idaas_client = idaas_client

    async def _get_auth_headers(self, said_token: str | None = None) -> dict[str, str]:
        """
        Get authentication headers with IDaaS token.

        Args:
            said_token: Optional SAID token for Client-Assertion header

        Returns:
            Dictionary of authentication headers
        """
        try:
            # Get M2M token from IDaaS
            m2m_token = await self.idaas_client.get_token_async()
            headers = {"Authorization": f"Bearer {m2m_token}"}

            # Add SAID token if provided
            if said_token:
                headers["Client-Assertion"] = f"Bearer {said_token}"

            return headers
        except Exception as e:
            raise RobotAuthenticationError(f"Failed to get authentication token: {e}") from e

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        json_data: dict[str, Any] | None = None,
        params: dict[str, Any] | None = None,
        said_token: str | None = None,
    ) -> dict[str, Any]:
        """
        Make authenticated HTTP request to robot API.

        Args:
            method: HTTP method
            endpoint: API endpoint
            json_data: JSON request body
            params: Query parameters
            said_token: Optional SAID token

        Returns:
            Response data as dictionary

        Raises:
            RobotClientError: For various error conditions
        """
        url = f"{self.base_url}{endpoint}"
        headers = await self._get_auth_headers(said_token)

        logger.debug(f"Making {method} request to {url}")

        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.request(method=method, url=url, json=json_data, params=params, headers=headers)

                # Handle HTTP errors
                if response.status_code == 401:
                    raise RobotAuthenticationError("Authentication failed")
                elif response.status_code == 404:
                    raise RobotNotFoundError("Resource not found")
                elif response.status_code == 422:
                    raise RobotValidationError("Request validation failed")
                elif response.status_code >= 400:
                    error_detail = "Unknown error"
                    try:
                        error_data = response.json()
                        error_detail = error_data.get("msg", error_detail)
                    except Exception:
                        error_detail = response.text

                    raise RobotClientError(
                        f"HTTP {response.status_code}: {error_detail}", status_code=response.status_code
                    )

                # Parse response
                try:
                    response_data = response.json()
                    # Ensure we return a dict[str, Any]
                    if isinstance(response_data, dict):
                        return response_data
                    else:
                        # If response is not a dict, wrap it in a dict
                        return {"data": response_data}
                except Exception as e:
                    raise RobotClientError(f"Failed to parse response: {e}") from e

        except httpx.TimeoutException as e:
            raise RobotNetworkError(f"Request timeout: {e}") from e
        except httpx.NetworkError as e:
            raise RobotNetworkError(f"Network error: {e}") from e
        except RobotClientError:
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            raise RobotClientError(f"Unexpected error: {e}") from e

    async def get_user_credentials(
        self, request: UserCredentialsRequest, said_token: str | None = None
    ) -> UserCredentialsResponse:
        """Get user authorization credentials."""
        try:
            # Convert request to API format
            api_request = {
                "search_type": request.search_type.value,
                "user_id": request.user_id,
                "services": [{"id": service.id, "scopes": service.scopes} for service in request.services],
            }

            response_data = await self._make_request(
                method="POST", endpoint="/api/v2/credentials", json_data=api_request, said_token=said_token
            )

            return UserCredentialsResponse(**response_data)

        except ValidationError as e:
            raise RobotValidationError(f"Response validation failed: {e}") from e

    async def create_conversation(self, request: CreateConversationRequest) -> ConversationResponse:
        """Create a new conversation."""
        try:
            api_request = {"title": request.title, "scenario": request.scenario, "metadata": request.metadata}

            response_data = await self._make_request(
                method="POST", endpoint="/api/v2/conversations", json_data=api_request
            )

            return ConversationResponse(**response_data)

        except ValidationError as e:
            raise RobotValidationError(f"Response validation failed: {e}") from e

    async def add_conversation_members(self, conversation_id: int, request: AddMembersRequest) -> AddMembersResponse:
        """Add members to an existing conversation."""
        try:
            api_request = {"id_type": request.id_type.value, "ids": request.ids}

            response_data = await self._make_request(
                method="PATCH", endpoint=f"/api/v2/conversations/{conversation_id}/members", json_data=api_request
            )

            return AddMembersResponse(**response_data)

        except ValidationError as e:
            raise RobotValidationError(f"Response validation failed: {e}") from e

    async def update_conversation(
        self, conversation_id: int, request: UpdateConversationRequest
    ) -> ConversationResponse:
        """Update conversation properties."""
        try:
            api_request = {"title": request.title}

            response_data = await self._make_request(
                method="PATCH", endpoint=f"/api/v2/conversations/{conversation_id}", json_data=api_request
            )

            return ConversationResponse(**response_data)

        except ValidationError as e:
            raise RobotValidationError(f"Response validation failed: {e}") from e

    async def send_message(self, conversation_id: int, request: SendMessageRequest) -> SendMessageResponse:
        """Send a message to a conversation."""
        try:
            api_request = {"parent_id": request.parent_id, "messages": request.messages}

            response_data = await self._make_request(
                method="POST",
                endpoint=f"/api/v2/conversations/{conversation_id}/messages",
                json_data=api_request,
                params={"stream": "false"},
            )

            return SendMessageResponse(**response_data)

        except ValidationError as e:
            raise RobotValidationError(f"Response validation failed: {e}") from e

    async def get_conversation_history(self, conversation_id: int) -> ConversationHistoryResponse:
        """Get conversation history."""
        try:
            response_data = await self._make_request(method="GET", endpoint=f"/api/v2/conversations/{conversation_id}")

            return ConversationHistoryResponse(**response_data)

        except ValidationError as e:
            raise RobotValidationError(f"Response validation failed: {e}") from e
